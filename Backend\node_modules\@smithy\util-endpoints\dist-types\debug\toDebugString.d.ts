import { EndpointParameters, EndpointV2 } from "@smithy/types";
import { GetAttrValue } from "../lib";
import { EndpointObject, FunctionObject, FunctionReturn } from "../types";
export declare function toDebugString(input: EndpointParameters): string;
export declare function toDebugString(input: EndpointV2): string;
export declare function toDebugString(input: GetAttrValue): string;
export declare function toDebugString(input: FunctionObject): string;
export declare function toDebugString(input: FunctionReturn): string;
export declare function toDebugString(input: EndpointObject): string;
