import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import { PutBucketPolicyRequest } from "../models/models_1";
import {
  S3ClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../S3Client";
export { __MetadataBearer };
export { $Command };
export interface PutBucketPolicyCommandInput extends PutBucketPolicyRequest {}
export interface PutBucketPolicyCommandOutput extends __MetadataBearer {}
declare const PutBucketPolicyCommand_base: {
  new (
    input: PutBucketPolicyCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    PutBucketPolicyCommandInput,
    PutBucketPolicyCommandOutput,
    S3ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: PutBucketPolicyCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    PutBucketPolicyCommandInput,
    PutBucketPolicyCommandOutput,
    S3ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class PutBucketPolicyCommand extends PutBucketPolicyCommand_base {
  protected static __types: {
    api: {
      input: PutBucketPolicyRequest;
      output: {};
    };
    sdk: {
      input: PutBucketPolicyCommandInput;
      output: PutBucketPolicyCommandOutput;
    };
  };
}
