import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import { PutBucketAclRequest } from "../models/models_1";
import {
  S3ClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../S3Client";
export { __MetadataBearer };
export { $Command };
export interface PutBucketAclCommandInput extends PutBucketAclRequest {}
export interface PutBucketAclCommandOutput extends __MetadataBearer {}
declare const PutBucketAclCommand_base: {
  new (
    input: PutBucketAclCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    PutBucketAclCommandInput,
    PutBucketAclCommandOutput,
    S3ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: PutBucketAclCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    PutBucketAclCommandInput,
    PutBucketAclCommandOutput,
    S3ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class PutBucketAclCommand extends PutBucketAclCommand_base {
  protected static __types: {
    api: {
      input: PutBucketAclRequest;
      output: {};
    };
    sdk: {
      input: PutBucketAclCommandInput;
      output: PutBucketAclCommandOutput;
    };
  };
}
