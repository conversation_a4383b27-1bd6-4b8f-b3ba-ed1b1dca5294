import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import { DeleteBucketIntelligentTieringConfigurationRequest } from "../models/models_0";
import {
  S3ClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../S3Client";
export { __MetadataBearer };
export { $Command };
export interface DeleteBucketIntelligentTieringConfigurationCommandInput
  extends DeleteBucketIntelligentTieringConfigurationRequest {}
export interface DeleteBucketIntelligentTieringConfigurationCommandOutput
  extends __MetadataBearer {}
declare const DeleteBucketIntelligentTieringConfigurationCommand_base: {
  new (
    input: DeleteBucketIntelligentTieringConfigurationCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteBucketIntelligentTieringConfigurationCommandInput,
    DeleteBucketIntelligentTieringConfigurationCommandOutput,
    S3ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DeleteBucketIntelligentTieringConfigurationCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteBucketIntelligentTieringConfigurationCommandInput,
    DeleteBucketIntelligentTieringConfigurationCommandOutput,
    S3ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DeleteBucketIntelligentTieringConfigurationCommand extends DeleteBucketIntelligentTieringConfigurationCommand_base {
  protected static __types: {
    api: {
      input: DeleteBucketIntelligentTieringConfigurationRequest;
      output: {};
    };
    sdk: {
      input: DeleteBucketIntelligentTieringConfigurationCommandInput;
      output: DeleteBucketIntelligentTieringConfigurationCommandOutput;
    };
  };
}
