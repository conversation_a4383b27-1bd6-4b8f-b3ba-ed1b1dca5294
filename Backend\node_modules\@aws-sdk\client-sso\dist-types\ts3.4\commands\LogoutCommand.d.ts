import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import { LogoutRequest } from "../models/models_0";
import {
  ServiceInputTypes,
  ServiceOutputTypes,
  SSOClientResolvedConfig,
} from "../SSOClient";
export { __MetadataBearer };
export { $Command };
export interface LogoutCommandInput extends LogoutRequest {}
export interface LogoutCommandOutput extends __MetadataBearer {}
declare const LogoutCommand_base: {
  new (input: LogoutCommandInput): import("@smithy/smithy-client").CommandImpl<
    LogoutCommandInput,
    LogoutCommandOutput,
    SSOClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (input: LogoutCommandInput): import("@smithy/smithy-client").CommandImpl<
    LogoutCommandInput,
    LogoutCommandOutput,
    SSOClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class LogoutCommand extends LogoutCommand_base {
  protected static __types: {
    api: {
      input: LogoutRequest;
      output: {};
    };
    sdk: {
      input: LogoutCommandInput;
      output: LogoutCommandOutput;
    };
  };
}
