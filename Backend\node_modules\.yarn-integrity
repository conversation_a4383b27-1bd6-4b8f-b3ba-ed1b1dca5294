{"systemParams": "win32-x64-127", "modulesFolders": ["node_modules"], "flags": [], "linkedModules": [], "topLevelPatterns": ["@aws-sdk/client-s3@^3.848.0", "@prisma/client@^6.11.1", "@types/bcrypt@^5.0.2", "@types/compression@^1.8.1", "@types/cors@^2.8.19", "@types/express@^5.0.3", "@types/jsonwebtoken@^9.0.10", "@types/ms@^2.1.0", "@types/multer-s3@^3.0.3", "@types/multer@^2.0.0", "@types/node@^24.0.7", "@types/nodemailer@^6.4.17", "bcrypt@^6.0.0", "compression@^1.8.0", "cors@^2.8.5", "dotenv@^17.0.0", "express-async-handler@^1.2.0", "express@^5.1.0", "google-auth-library@^10.1.0", "helmet@^8.1.0", "jsonwebtoken@^9.0.2", "mongoose@^8.16.1", "multer-s3@^3.0.1", "m<PERSON>@^2.0.2", "nodemailer@^7.0.4", "nodemon@^3.1.10", "prisma@^6.11.1", "ts-node@^10.9.2", "typescript@^5.8.3"], "lockfileEntries": {"@aws-crypto/crc32@5.2.0": "https://registry.npmjs.org/@aws-crypto/crc32/-/crc32-5.2.0.tgz", "@aws-crypto/crc32c@5.2.0": "https://registry.npmjs.org/@aws-crypto/crc32c/-/crc32c-5.2.0.tgz", "@aws-crypto/sha1-browser@5.2.0": "https://registry.npmjs.org/@aws-crypto/sha1-browser/-/sha1-browser-5.2.0.tgz", "@aws-crypto/sha256-browser@5.2.0": "https://registry.npmjs.org/@aws-crypto/sha256-browser/-/sha256-browser-5.2.0.tgz", "@aws-crypto/sha256-js@5.2.0": "https://registry.npmjs.org/@aws-crypto/sha256-js/-/sha256-js-5.2.0.tgz", "@aws-crypto/sha256-js@^5.2.0": "https://registry.npmjs.org/@aws-crypto/sha256-js/-/sha256-js-5.2.0.tgz", "@aws-crypto/supports-web-crypto@^5.2.0": "https://registry.npmjs.org/@aws-crypto/supports-web-crypto/-/supports-web-crypto-5.2.0.tgz", "@aws-crypto/util@5.2.0": "https://registry.npmjs.org/@aws-crypto/util/-/util-5.2.0.tgz", "@aws-crypto/util@^5.2.0": "https://registry.npmjs.org/@aws-crypto/util/-/util-5.2.0.tgz", "@aws-sdk/client-s3@^3.0.0": "https://registry.npmjs.org/@aws-sdk/client-s3/-/client-s3-3.848.0.tgz", "@aws-sdk/client-s3@^3.848.0": "https://registry.yarnpkg.com/@aws-sdk/client-s3/-/client-s3-3.850.0.tgz#a831d99d4c06332d9784c6a2d49f082ec8d98186", "@aws-sdk/client-sso@3.848.0": "https://registry.npmjs.org/@aws-sdk/client-sso/-/client-sso-3.848.0.tgz", "@aws-sdk/core@3.846.0": "https://registry.npmjs.org/@aws-sdk/core/-/core-3.846.0.tgz", "@aws-sdk/credential-provider-env@3.846.0": "https://registry.npmjs.org/@aws-sdk/credential-provider-env/-/credential-provider-env-3.846.0.tgz", "@aws-sdk/credential-provider-http@3.846.0": "https://registry.npmjs.org/@aws-sdk/credential-provider-http/-/credential-provider-http-3.846.0.tgz", "@aws-sdk/credential-provider-ini@3.848.0": "https://registry.npmjs.org/@aws-sdk/credential-provider-ini/-/credential-provider-ini-3.848.0.tgz", "@aws-sdk/credential-provider-node@3.848.0": "https://registry.npmjs.org/@aws-sdk/credential-provider-node/-/credential-provider-node-3.848.0.tgz", "@aws-sdk/credential-provider-process@3.846.0": "https://registry.npmjs.org/@aws-sdk/credential-provider-process/-/credential-provider-process-3.846.0.tgz", "@aws-sdk/credential-provider-sso@3.848.0": "https://registry.npmjs.org/@aws-sdk/credential-provider-sso/-/credential-provider-sso-3.848.0.tgz", "@aws-sdk/credential-provider-web-identity@3.848.0": "https://registry.npmjs.org/@aws-sdk/credential-provider-web-identity/-/credential-provider-web-identity-3.848.0.tgz", "@aws-sdk/lib-storage@^3.46.0": "https://registry.npmjs.org/@aws-sdk/lib-storage/-/lib-storage-3.848.0.tgz", "@aws-sdk/middleware-bucket-endpoint@3.840.0": "https://registry.npmjs.org/@aws-sdk/middleware-bucket-endpoint/-/middleware-bucket-endpoint-3.840.0.tgz", "@aws-sdk/middleware-expect-continue@3.840.0": "https://registry.npmjs.org/@aws-sdk/middleware-expect-continue/-/middleware-expect-continue-3.840.0.tgz", "@aws-sdk/middleware-flexible-checksums@3.846.0": "https://registry.npmjs.org/@aws-sdk/middleware-flexible-checksums/-/middleware-flexible-checksums-3.846.0.tgz", "@aws-sdk/middleware-host-header@3.840.0": "https://registry.npmjs.org/@aws-sdk/middleware-host-header/-/middleware-host-header-3.840.0.tgz", "@aws-sdk/middleware-location-constraint@3.840.0": "https://registry.npmjs.org/@aws-sdk/middleware-location-constraint/-/middleware-location-constraint-3.840.0.tgz", "@aws-sdk/middleware-logger@3.840.0": "https://registry.npmjs.org/@aws-sdk/middleware-logger/-/middleware-logger-3.840.0.tgz", "@aws-sdk/middleware-recursion-detection@3.840.0": "https://registry.npmjs.org/@aws-sdk/middleware-recursion-detection/-/middleware-recursion-detection-3.840.0.tgz", "@aws-sdk/middleware-sdk-s3@3.846.0": "https://registry.npmjs.org/@aws-sdk/middleware-sdk-s3/-/middleware-sdk-s3-3.846.0.tgz", "@aws-sdk/middleware-ssec@3.840.0": "https://registry.npmjs.org/@aws-sdk/middleware-ssec/-/middleware-ssec-3.840.0.tgz", "@aws-sdk/middleware-user-agent@3.848.0": "https://registry.npmjs.org/@aws-sdk/middleware-user-agent/-/middleware-user-agent-3.848.0.tgz", "@aws-sdk/nested-clients@3.848.0": "https://registry.npmjs.org/@aws-sdk/nested-clients/-/nested-clients-3.848.0.tgz", "@aws-sdk/region-config-resolver@3.840.0": "https://registry.npmjs.org/@aws-sdk/region-config-resolver/-/region-config-resolver-3.840.0.tgz", "@aws-sdk/signature-v4-multi-region@3.846.0": "https://registry.npmjs.org/@aws-sdk/signature-v4-multi-region/-/signature-v4-multi-region-3.846.0.tgz", "@aws-sdk/token-providers@3.848.0": "https://registry.npmjs.org/@aws-sdk/token-providers/-/token-providers-3.848.0.tgz", "@aws-sdk/types@3.840.0": "https://registry.npmjs.org/@aws-sdk/types/-/types-3.840.0.tgz", "@aws-sdk/types@^3.222.0": "https://registry.npmjs.org/@aws-sdk/types/-/types-3.840.0.tgz", "@aws-sdk/util-arn-parser@3.804.0": "https://registry.npmjs.org/@aws-sdk/util-arn-parser/-/util-arn-parser-3.804.0.tgz", "@aws-sdk/util-endpoints@3.848.0": "https://registry.npmjs.org/@aws-sdk/util-endpoints/-/util-endpoints-3.848.0.tgz", "@aws-sdk/util-locate-window@^3.0.0": "https://registry.npmjs.org/@aws-sdk/util-locate-window/-/util-locate-window-3.804.0.tgz", "@aws-sdk/util-user-agent-browser@3.840.0": "https://registry.npmjs.org/@aws-sdk/util-user-agent-browser/-/util-user-agent-browser-3.840.0.tgz", "@aws-sdk/util-user-agent-node@3.848.0": "https://registry.npmjs.org/@aws-sdk/util-user-agent-node/-/util-user-agent-node-3.848.0.tgz", "@aws-sdk/xml-builder@3.821.0": "https://registry.npmjs.org/@aws-sdk/xml-builder/-/xml-builder-3.821.0.tgz", "@cspotcode/source-map-support@^0.8.0": "https://registry.npmjs.org/@cspotcode/source-map-support/-/source-map-support-0.8.1.tgz", "@jridgewell/resolve-uri@^3.0.3": "https://registry.npmjs.org/@jridgewell/resolve-uri/-/resolve-uri-3.1.2.tgz", "@jridgewell/sourcemap-codec@^1.4.10": "https://registry.npmjs.org/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.5.4.tgz", "@jridgewell/trace-mapping@0.3.9": "https://registry.npmjs.org/@jridgewell/trace-mapping/-/trace-mapping-0.3.9.tgz", "@mongodb-js/saslprep@^1.1.9": "https://registry.npmjs.org/@mongodb-js/saslprep/-/saslprep-1.3.0.tgz", "@prisma/client@^6.11.1": "https://registry.npmjs.org/@prisma/client/-/client-6.12.0.tgz", "@prisma/config@6.12.0": "https://registry.npmjs.org/@prisma/config/-/config-6.12.0.tgz", "@prisma/debug@6.12.0": "https://registry.npmjs.org/@prisma/debug/-/debug-6.12.0.tgz", "@prisma/engines-version@6.12.0-15.8047c96bbd92db98a2abc7c9323ce77c02c89dbc": "https://registry.npmjs.org/@prisma/engines-version/-/engines-version-6.12.0-15.8047c96bbd92db98a2abc7c9323ce77c02c89dbc.tgz", "@prisma/engines@6.12.0": "https://registry.npmjs.org/@prisma/engines/-/engines-6.12.0.tgz", "@prisma/fetch-engine@6.12.0": "https://registry.npmjs.org/@prisma/fetch-engine/-/fetch-engine-6.12.0.tgz", "@prisma/get-platform@6.12.0": "https://registry.npmjs.org/@prisma/get-platform/-/get-platform-6.12.0.tgz", "@smithy/abort-controller@^4.0.4": "https://registry.npmjs.org/@smithy/abort-controller/-/abort-controller-4.0.4.tgz", "@smithy/chunked-blob-reader-native@^4.0.0": "https://registry.npmjs.org/@smithy/chunked-blob-reader-native/-/chunked-blob-reader-native-4.0.0.tgz", "@smithy/chunked-blob-reader@^5.0.0": "https://registry.npmjs.org/@smithy/chunked-blob-reader/-/chunked-blob-reader-5.0.0.tgz", "@smithy/config-resolver@^4.1.4": "https://registry.npmjs.org/@smithy/config-resolver/-/config-resolver-4.1.4.tgz", "@smithy/core@^3.7.0": "https://registry.npmjs.org/@smithy/core/-/core-3.7.1.tgz", "@smithy/core@^3.7.1": "https://registry.npmjs.org/@smithy/core/-/core-3.7.1.tgz", "@smithy/credential-provider-imds@^4.0.6": "https://registry.npmjs.org/@smithy/credential-provider-imds/-/credential-provider-imds-4.0.6.tgz", "@smithy/eventstream-codec@^4.0.4": "https://registry.npmjs.org/@smithy/eventstream-codec/-/eventstream-codec-4.0.4.tgz", "@smithy/eventstream-serde-browser@^4.0.4": "https://registry.npmjs.org/@smithy/eventstream-serde-browser/-/eventstream-serde-browser-4.0.4.tgz", "@smithy/eventstream-serde-config-resolver@^4.1.2": "https://registry.npmjs.org/@smithy/eventstream-serde-config-resolver/-/eventstream-serde-config-resolver-4.1.2.tgz", "@smithy/eventstream-serde-node@^4.0.4": "https://registry.npmjs.org/@smithy/eventstream-serde-node/-/eventstream-serde-node-4.0.4.tgz", "@smithy/eventstream-serde-universal@^4.0.4": "https://registry.npmjs.org/@smithy/eventstream-serde-universal/-/eventstream-serde-universal-4.0.4.tgz", "@smithy/fetch-http-handler@^5.1.0": "https://registry.npmjs.org/@smithy/fetch-http-handler/-/fetch-http-handler-5.1.0.tgz", "@smithy/hash-blob-browser@^4.0.4": "https://registry.npmjs.org/@smithy/hash-blob-browser/-/hash-blob-browser-4.0.4.tgz", "@smithy/hash-node@^4.0.4": "https://registry.npmjs.org/@smithy/hash-node/-/hash-node-4.0.4.tgz", "@smithy/hash-stream-node@^4.0.4": "https://registry.npmjs.org/@smithy/hash-stream-node/-/hash-stream-node-4.0.4.tgz", "@smithy/invalid-dependency@^4.0.4": "https://registry.npmjs.org/@smithy/invalid-dependency/-/invalid-dependency-4.0.4.tgz", "@smithy/is-array-buffer@^2.2.0": "https://registry.npmjs.org/@smithy/is-array-buffer/-/is-array-buffer-2.2.0.tgz", "@smithy/is-array-buffer@^4.0.0": "https://registry.npmjs.org/@smithy/is-array-buffer/-/is-array-buffer-4.0.0.tgz", "@smithy/md5-js@^4.0.4": "https://registry.npmjs.org/@smithy/md5-js/-/md5-js-4.0.4.tgz", "@smithy/middleware-content-length@^4.0.4": "https://registry.npmjs.org/@smithy/middleware-content-length/-/middleware-content-length-4.0.4.tgz", "@smithy/middleware-endpoint@^4.1.15": "https://registry.npmjs.org/@smithy/middleware-endpoint/-/middleware-endpoint-4.1.16.tgz", "@smithy/middleware-endpoint@^4.1.16": "https://registry.npmjs.org/@smithy/middleware-endpoint/-/middleware-endpoint-4.1.16.tgz", "@smithy/middleware-retry@^4.1.16": "https://registry.npmjs.org/@smithy/middleware-retry/-/middleware-retry-4.1.17.tgz", "@smithy/middleware-serde@^4.0.8": "https://registry.npmjs.org/@smithy/middleware-serde/-/middleware-serde-4.0.8.tgz", "@smithy/middleware-stack@^4.0.4": "https://registry.npmjs.org/@smithy/middleware-stack/-/middleware-stack-4.0.4.tgz", "@smithy/node-config-provider@^4.1.3": "https://registry.npmjs.org/@smithy/node-config-provider/-/node-config-provider-4.1.3.tgz", "@smithy/node-http-handler@^4.1.0": "https://registry.npmjs.org/@smithy/node-http-handler/-/node-http-handler-4.1.0.tgz", "@smithy/property-provider@^4.0.4": "https://registry.npmjs.org/@smithy/property-provider/-/property-provider-4.0.4.tgz", "@smithy/protocol-http@^5.1.2": "https://registry.npmjs.org/@smithy/protocol-http/-/protocol-http-5.1.2.tgz", "@smithy/querystring-builder@^4.0.4": "https://registry.npmjs.org/@smithy/querystring-builder/-/querystring-builder-4.0.4.tgz", "@smithy/querystring-parser@^4.0.4": "https://registry.npmjs.org/@smithy/querystring-parser/-/querystring-parser-4.0.4.tgz", "@smithy/service-error-classification@^4.0.6": "https://registry.npmjs.org/@smithy/service-error-classification/-/service-error-classification-4.0.6.tgz", "@smithy/shared-ini-file-loader@^4.0.4": "https://registry.npmjs.org/@smithy/shared-ini-file-loader/-/shared-ini-file-loader-4.0.4.tgz", "@smithy/signature-v4@^5.1.2": "https://registry.npmjs.org/@smithy/signature-v4/-/signature-v4-5.1.2.tgz", "@smithy/smithy-client@^4.4.7": "https://registry.npmjs.org/@smithy/smithy-client/-/smithy-client-4.4.8.tgz", "@smithy/smithy-client@^4.4.8": "https://registry.npmjs.org/@smithy/smithy-client/-/smithy-client-4.4.8.tgz", "@smithy/types@^4.3.1": "https://registry.npmjs.org/@smithy/types/-/types-4.3.1.tgz", "@smithy/url-parser@^4.0.4": "https://registry.npmjs.org/@smithy/url-parser/-/url-parser-4.0.4.tgz", "@smithy/util-base64@^4.0.0": "https://registry.npmjs.org/@smithy/util-base64/-/util-base64-4.0.0.tgz", "@smithy/util-body-length-browser@^4.0.0": "https://registry.npmjs.org/@smithy/util-body-length-browser/-/util-body-length-browser-4.0.0.tgz", "@smithy/util-body-length-node@^4.0.0": "https://registry.npmjs.org/@smithy/util-body-length-node/-/util-body-length-node-4.0.0.tgz", "@smithy/util-buffer-from@^2.2.0": "https://registry.npmjs.org/@smithy/util-buffer-from/-/util-buffer-from-2.2.0.tgz", "@smithy/util-buffer-from@^4.0.0": "https://registry.npmjs.org/@smithy/util-buffer-from/-/util-buffer-from-4.0.0.tgz", "@smithy/util-config-provider@^4.0.0": "https://registry.npmjs.org/@smithy/util-config-provider/-/util-config-provider-4.0.0.tgz", "@smithy/util-defaults-mode-browser@^4.0.23": "https://registry.npmjs.org/@smithy/util-defaults-mode-browser/-/util-defaults-mode-browser-4.0.24.tgz", "@smithy/util-defaults-mode-node@^4.0.23": "https://registry.npmjs.org/@smithy/util-defaults-mode-node/-/util-defaults-mode-node-4.0.24.tgz", "@smithy/util-endpoints@^3.0.6": "https://registry.npmjs.org/@smithy/util-endpoints/-/util-endpoints-3.0.6.tgz", "@smithy/util-hex-encoding@^4.0.0": "https://registry.npmjs.org/@smithy/util-hex-encoding/-/util-hex-encoding-4.0.0.tgz", "@smithy/util-middleware@^4.0.4": "https://registry.npmjs.org/@smithy/util-middleware/-/util-middleware-4.0.4.tgz", "@smithy/util-retry@^4.0.6": "https://registry.npmjs.org/@smithy/util-retry/-/util-retry-4.0.6.tgz", "@smithy/util-stream@^4.2.3": "https://registry.npmjs.org/@smithy/util-stream/-/util-stream-4.2.3.tgz", "@smithy/util-uri-escape@^4.0.0": "https://registry.npmjs.org/@smithy/util-uri-escape/-/util-uri-escape-4.0.0.tgz", "@smithy/util-utf8@^2.0.0": "https://registry.npmjs.org/@smithy/util-utf8/-/util-utf8-2.3.0.tgz", "@smithy/util-utf8@^4.0.0": "https://registry.npmjs.org/@smithy/util-utf8/-/util-utf8-4.0.0.tgz", "@smithy/util-waiter@^4.0.6": "https://registry.npmjs.org/@smithy/util-waiter/-/util-waiter-4.0.6.tgz", "@tsconfig/node10@^1.0.7": "https://registry.npmjs.org/@tsconfig/node10/-/node10-1.0.11.tgz", "@tsconfig/node12@^1.0.7": "https://registry.npmjs.org/@tsconfig/node12/-/node12-1.0.11.tgz", "@tsconfig/node14@^1.0.0": "https://registry.npmjs.org/@tsconfig/node14/-/node14-1.0.3.tgz", "@tsconfig/node16@^1.0.2": "https://registry.npmjs.org/@tsconfig/node16/-/node16-1.0.4.tgz", "@types/bcrypt@^5.0.2": "https://registry.npmjs.org/@types/bcrypt/-/bcrypt-5.0.2.tgz", "@types/body-parser@*": "https://registry.npmjs.org/@types/body-parser/-/body-parser-1.19.6.tgz", "@types/compression@^1.8.1": "https://registry.npmjs.org/@types/compression/-/compression-1.8.1.tgz", "@types/connect@*": "https://registry.npmjs.org/@types/connect/-/connect-3.4.38.tgz", "@types/cors@^2.8.19": "https://registry.npmjs.org/@types/cors/-/cors-2.8.19.tgz", "@types/express-serve-static-core@^5.0.0": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-5.0.7.tgz", "@types/express@*": "https://registry.npmjs.org/@types/express/-/express-5.0.3.tgz", "@types/express@^5.0.3": "https://registry.npmjs.org/@types/express/-/express-5.0.3.tgz", "@types/http-errors@*": "https://registry.npmjs.org/@types/http-errors/-/http-errors-2.0.5.tgz", "@types/jsonwebtoken@^9.0.10": "https://registry.npmjs.org/@types/jsonwebtoken/-/jsonwebtoken-9.0.10.tgz", "@types/mime@^1": "https://registry.npmjs.org/@types/mime/-/mime-1.3.5.tgz", "@types/ms@*": "https://registry.npmjs.org/@types/ms/-/ms-2.1.0.tgz", "@types/ms@^2.1.0": "https://registry.npmjs.org/@types/ms/-/ms-2.1.0.tgz", "@types/multer-s3@^3.0.3": "https://registry.npmjs.org/@types/multer-s3/-/multer-s3-3.0.3.tgz", "@types/multer@*": "https://registry.npmjs.org/@types/multer/-/multer-2.0.0.tgz", "@types/multer@^2.0.0": "https://registry.npmjs.org/@types/multer/-/multer-2.0.0.tgz", "@types/node@*": "https://registry.npmjs.org/@types/node/-/node-24.0.14.tgz", "@types/node@^24.0.7": "https://registry.npmjs.org/@types/node/-/node-24.0.14.tgz", "@types/nodemailer@^6.4.17": "https://registry.npmjs.org/@types/nodemailer/-/nodemailer-6.4.17.tgz", "@types/qs@*": "https://registry.npmjs.org/@types/qs/-/qs-6.14.0.tgz", "@types/range-parser@*": "https://registry.npmjs.org/@types/range-parser/-/range-parser-1.2.7.tgz", "@types/send@*": "https://registry.npmjs.org/@types/send/-/send-0.17.5.tgz", "@types/serve-static@*": "https://registry.npmjs.org/@types/serve-static/-/serve-static-1.15.8.tgz", "@types/uuid@^9.0.1": "https://registry.npmjs.org/@types/uuid/-/uuid-9.0.8.tgz", "@types/webidl-conversions@*": "https://registry.npmjs.org/@types/webidl-conversions/-/webidl-conversions-7.0.3.tgz", "@types/whatwg-url@^11.0.2": "https://registry.npmjs.org/@types/whatwg-url/-/whatwg-url-11.0.5.tgz", "accepts@^2.0.0": "https://registry.npmjs.org/accepts/-/accepts-2.0.0.tgz", "acorn-walk@^8.1.1": "https://registry.npmjs.org/acorn-walk/-/acorn-walk-8.3.4.tgz", "acorn@^8.11.0": "https://registry.npmjs.org/acorn/-/acorn-8.15.0.tgz", "acorn@^8.4.1": "https://registry.npmjs.org/acorn/-/acorn-8.15.0.tgz", "agent-base@^7.1.2": "https://registry.npmjs.org/agent-base/-/agent-base-7.1.4.tgz", "anymatch@~3.1.2": "https://registry.npmjs.org/anymatch/-/anymatch-3.1.3.tgz", "append-field@^1.0.0": "https://registry.npmjs.org/append-field/-/append-field-1.0.0.tgz", "arg@^4.1.0": "https://registry.npmjs.org/arg/-/arg-4.1.3.tgz", "balanced-match@^1.0.0": "https://registry.npmjs.org/balanced-match/-/balanced-match-1.0.2.tgz", "base64-js@^1.0.2": "https://registry.npmjs.org/base64-js/-/base64-js-1.5.1.tgz", "base64-js@^1.3.0": "https://registry.npmjs.org/base64-js/-/base64-js-1.5.1.tgz", "bcrypt@^6.0.0": "https://registry.npmjs.org/bcrypt/-/bcrypt-6.0.0.tgz", "bignumber.js@^9.0.0": "https://registry.npmjs.org/bignumber.js/-/bignumber.js-9.3.1.tgz", "binary-extensions@^2.0.0": "https://registry.npmjs.org/binary-extensions/-/binary-extensions-2.3.0.tgz", "body-parser@^2.2.0": "https://registry.npmjs.org/body-parser/-/body-parser-2.2.0.tgz", "bowser@^2.11.0": "https://registry.npmjs.org/bowser/-/bowser-2.11.0.tgz", "brace-expansion@^1.1.7": "https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.12.tgz", "braces@~3.0.2": "https://registry.npmjs.org/braces/-/braces-3.0.3.tgz", "bson@^6.10.4": "https://registry.npmjs.org/bson/-/bson-6.10.4.tgz", "buffer-equal-constant-time@^1.0.1": "https://registry.npmjs.org/buffer-equal-constant-time/-/buffer-equal-constant-time-1.0.1.tgz", "buffer-from@^1.0.0": "https://registry.npmjs.org/buffer-from/-/buffer-from-1.1.2.tgz", "buffer@5.6.0": "https://registry.npmjs.org/buffer/-/buffer-5.6.0.tgz", "busboy@^1.6.0": "https://registry.npmjs.org/busboy/-/busboy-1.6.0.tgz", "bytes@3.1.2": "https://registry.npmjs.org/bytes/-/bytes-3.1.2.tgz", "bytes@^3.1.2": "https://registry.npmjs.org/bytes/-/bytes-3.1.2.tgz", "call-bind-apply-helpers@^1.0.1": "https://registry.npmjs.org/call-bind-apply-helpers/-/call-bind-apply-helpers-1.0.2.tgz", "call-bind-apply-helpers@^1.0.2": "https://registry.npmjs.org/call-bind-apply-helpers/-/call-bind-apply-helpers-1.0.2.tgz", "call-bound@^1.0.2": "https://registry.npmjs.org/call-bound/-/call-bound-1.0.4.tgz", "chokidar@^3.5.2": "https://registry.npmjs.org/chokidar/-/chokidar-3.6.0.tgz", "compressible@~2.0.18": "https://registry.npmjs.org/compressible/-/compressible-2.0.18.tgz", "compression@^1.8.0": "https://registry.npmjs.org/compression/-/compression-1.8.1.tgz", "concat-map@0.0.1": "https://registry.npmjs.org/concat-map/-/concat-map-0.0.1.tgz", "concat-stream@^2.0.0": "https://registry.npmjs.org/concat-stream/-/concat-stream-2.0.0.tgz", "content-disposition@^1.0.0": "https://registry.npmjs.org/content-disposition/-/content-disposition-1.0.0.tgz", "content-type@^1.0.5": "https://registry.npmjs.org/content-type/-/content-type-1.0.5.tgz", "cookie-signature@^1.2.1": "https://registry.npmjs.org/cookie-signature/-/cookie-signature-1.2.2.tgz", "cookie@^0.7.1": "https://registry.npmjs.org/cookie/-/cookie-0.7.2.tgz", "cors@^2.8.5": "https://registry.npmjs.org/cors/-/cors-2.8.5.tgz", "create-require@^1.1.0": "https://registry.npmjs.org/create-require/-/create-require-1.1.1.tgz", "data-uri-to-buffer@^4.0.0": "https://registry.npmjs.org/data-uri-to-buffer/-/data-uri-to-buffer-4.0.1.tgz", "debug@2.6.9": "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz", "debug@4": "https://registry.npmjs.org/debug/-/debug-4.4.1.tgz", "debug@4.x": "https://registry.npmjs.org/debug/-/debug-4.4.1.tgz", "debug@^4": "https://registry.npmjs.org/debug/-/debug-4.4.1.tgz", "debug@^4.3.5": "https://registry.npmjs.org/debug/-/debug-4.4.1.tgz", "debug@^4.4.0": "https://registry.npmjs.org/debug/-/debug-4.4.1.tgz", "depd@2.0.0": "https://registry.npmjs.org/depd/-/depd-2.0.0.tgz", "depd@^2.0.0": "https://registry.npmjs.org/depd/-/depd-2.0.0.tgz", "diff@^4.0.1": "https://registry.npmjs.org/diff/-/diff-4.0.2.tgz", "dotenv@^17.0.0": "https://registry.npmjs.org/dotenv/-/dotenv-17.2.0.tgz", "dunder-proto@^1.0.1": "https://registry.npmjs.org/dunder-proto/-/dunder-proto-1.0.1.tgz", "ecdsa-sig-formatter@1.0.11": "https://registry.npmjs.org/ecdsa-sig-formatter/-/ecdsa-sig-formatter-1.0.11.tgz", "ecdsa-sig-formatter@^1.0.11": "https://registry.npmjs.org/ecdsa-sig-formatter/-/ecdsa-sig-formatter-1.0.11.tgz", "ee-first@1.1.1": "https://registry.npmjs.org/ee-first/-/ee-first-1.1.1.tgz", "encodeurl@^2.0.0": "https://registry.npmjs.org/encodeurl/-/encodeurl-2.0.0.tgz", "es-define-property@^1.0.1": "https://registry.npmjs.org/es-define-property/-/es-define-property-1.0.1.tgz", "es-errors@^1.3.0": "https://registry.npmjs.org/es-errors/-/es-errors-1.3.0.tgz", "es-object-atoms@^1.0.0": "https://registry.npmjs.org/es-object-atoms/-/es-object-atoms-1.1.1.tgz", "es-object-atoms@^1.1.1": "https://registry.npmjs.org/es-object-atoms/-/es-object-atoms-1.1.1.tgz", "escape-html@^1.0.3": "https://registry.npmjs.org/escape-html/-/escape-html-1.0.3.tgz", "etag@^1.8.1": "https://registry.npmjs.org/etag/-/etag-1.8.1.tgz", "events@3.3.0": "https://registry.npmjs.org/events/-/events-3.3.0.tgz", "express-async-handler@^1.2.0": "https://registry.npmjs.org/express-async-handler/-/express-async-handler-1.2.0.tgz", "express@^5.1.0": "https://registry.npmjs.org/express/-/express-5.1.0.tgz", "extend@^3.0.2": "https://registry.npmjs.org/extend/-/extend-3.0.2.tgz", "fast-xml-parser@5.2.5": "https://registry.npmjs.org/fast-xml-parser/-/fast-xml-parser-5.2.5.tgz", "fetch-blob@^3.1.2": "https://registry.npmjs.org/fetch-blob/-/fetch-blob-3.2.0.tgz", "fetch-blob@^3.1.4": "https://registry.npmjs.org/fetch-blob/-/fetch-blob-3.2.0.tgz", "file-type@^3.3.0": "https://registry.npmjs.org/file-type/-/file-type-3.9.0.tgz", "fill-range@^7.1.1": "https://registry.npmjs.org/fill-range/-/fill-range-7.1.1.tgz", "finalhandler@^2.1.0": "https://registry.npmjs.org/finalhandler/-/finalhandler-2.1.0.tgz", "formdata-polyfill@^4.0.10": "https://registry.npmjs.org/formdata-polyfill/-/formdata-polyfill-4.0.10.tgz", "forwarded@0.2.0": "https://registry.npmjs.org/forwarded/-/forwarded-0.2.0.tgz", "fresh@^2.0.0": "https://registry.npmjs.org/fresh/-/fresh-2.0.0.tgz", "fsevents@~2.3.2": "https://registry.yarnpkg.com/fsevents/-/fsevents-2.3.3.tgz#cac6407785d03675a2a5e1a5305c697b347d90d6", "function-bind@^1.1.2": "https://registry.npmjs.org/function-bind/-/function-bind-1.1.2.tgz", "gaxios@^7.0.0": "https://registry.npmjs.org/gaxios/-/gaxios-7.1.1.tgz", "gcp-metadata@^7.0.0": "https://registry.npmjs.org/gcp-metadata/-/gcp-metadata-7.0.1.tgz", "get-intrinsic@^1.2.5": "https://registry.npmjs.org/get-intrinsic/-/get-intrinsic-1.3.0.tgz", "get-intrinsic@^1.3.0": "https://registry.npmjs.org/get-intrinsic/-/get-intrinsic-1.3.0.tgz", "get-proto@^1.0.1": "https://registry.npmjs.org/get-proto/-/get-proto-1.0.1.tgz", "glob-parent@~5.1.2": "https://registry.npmjs.org/glob-parent/-/glob-parent-5.1.2.tgz", "google-auth-library@^10.1.0": "https://registry.npmjs.org/google-auth-library/-/google-auth-library-10.1.0.tgz", "google-logging-utils@^1.0.0": "https://registry.npmjs.org/google-logging-utils/-/google-logging-utils-1.1.1.tgz", "gopd@^1.2.0": "https://registry.npmjs.org/gopd/-/gopd-1.2.0.tgz", "gtoken@^8.0.0": "https://registry.npmjs.org/gtoken/-/gtoken-8.0.0.tgz", "has-flag@^3.0.0": "https://registry.npmjs.org/has-flag/-/has-flag-3.0.0.tgz", "has-symbols@^1.1.0": "https://registry.npmjs.org/has-symbols/-/has-symbols-1.1.0.tgz", "hasown@^2.0.2": "https://registry.npmjs.org/hasown/-/hasown-2.0.2.tgz", "helmet@^8.1.0": "https://registry.npmjs.org/helmet/-/helmet-8.1.0.tgz", "html-comment-regex@^1.1.2": "https://registry.npmjs.org/html-comment-regex/-/html-comment-regex-1.1.2.tgz", "http-errors@2.0.0": "https://registry.npmjs.org/http-errors/-/http-errors-2.0.0.tgz", "http-errors@^2.0.0": "https://registry.npmjs.org/http-errors/-/http-errors-2.0.0.tgz", "https-proxy-agent@^7.0.1": "https://registry.npmjs.org/https-proxy-agent/-/https-proxy-agent-7.0.6.tgz", "iconv-lite@0.6.3": "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.6.3.tgz", "iconv-lite@^0.6.3": "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.6.3.tgz", "ieee754@^1.1.4": "https://registry.npmjs.org/ieee754/-/ieee754-1.1.13.tgz", "ignore-by-default@^1.0.1": "https://registry.npmjs.org/ignore-by-default/-/ignore-by-default-1.0.1.tgz", "inherits@2.0.4": "https://registry.npmjs.org/inherits/-/inherits-2.0.4.tgz", "inherits@^2.0.3": "https://registry.npmjs.org/inherits/-/inherits-2.0.4.tgz", "inherits@~2.0.4": "https://registry.npmjs.org/inherits/-/inherits-2.0.4.tgz", "ipaddr.js@1.9.1": "https://registry.npmjs.org/ipaddr.js/-/ipaddr.js-1.9.1.tgz", "is-binary-path@~2.1.0": "https://registry.npmjs.org/is-binary-path/-/is-binary-path-2.1.0.tgz", "is-extglob@^2.1.1": "https://registry.npmjs.org/is-extglob/-/is-extglob-2.1.1.tgz", "is-glob@^4.0.1": "https://registry.npmjs.org/is-glob/-/is-glob-4.0.3.tgz", "is-glob@~4.0.1": "https://registry.npmjs.org/is-glob/-/is-glob-4.0.3.tgz", "is-number@^7.0.0": "https://registry.npmjs.org/is-number/-/is-number-7.0.0.tgz", "is-promise@^4.0.0": "https://registry.npmjs.org/is-promise/-/is-promise-4.0.0.tgz", "jiti@2.4.2": "https://registry.npmjs.org/jiti/-/jiti-2.4.2.tgz", "json-bigint@^1.0.0": "https://registry.npmjs.org/json-bigint/-/json-bigint-1.0.0.tgz", "jsonwebtoken@^9.0.2": "https://registry.npmjs.org/jsonwebtoken/-/jsonwebtoken-9.0.2.tgz", "jwa@^1.4.1": "https://registry.npmjs.org/jwa/-/jwa-1.4.2.tgz", "jwa@^2.0.0": "https://registry.npmjs.org/jwa/-/jwa-2.0.1.tgz", "jws@^3.2.2": "https://registry.npmjs.org/jws/-/jws-3.2.2.tgz", "jws@^4.0.0": "https://registry.npmjs.org/jws/-/jws-4.0.0.tgz", "kareem@2.6.3": "https://registry.npmjs.org/kareem/-/kareem-2.6.3.tgz", "lodash.includes@^4.3.0": "https://registry.npmjs.org/lodash.includes/-/lodash.includes-4.3.0.tgz", "lodash.isboolean@^3.0.3": "https://registry.npmjs.org/lodash.isboolean/-/lodash.isboolean-3.0.3.tgz", "lodash.isinteger@^4.0.4": "https://registry.npmjs.org/lodash.isinteger/-/lodash.isinteger-4.0.4.tgz", "lodash.isnumber@^3.0.3": "https://registry.npmjs.org/lodash.isnumber/-/lodash.isnumber-3.0.3.tgz", "lodash.isplainobject@^4.0.6": "https://registry.npmjs.org/lodash.isplainobject/-/lodash.isplainobject-4.0.6.tgz", "lodash.isstring@^4.0.1": "https://registry.npmjs.org/lodash.isstring/-/lodash.isstring-4.0.1.tgz", "lodash.once@^4.0.0": "https://registry.npmjs.org/lodash.once/-/lodash.once-4.1.1.tgz", "make-error@^1.1.1": "https://registry.npmjs.org/make-error/-/make-error-1.3.6.tgz", "math-intrinsics@^1.1.0": "https://registry.npmjs.org/math-intrinsics/-/math-intrinsics-1.1.0.tgz", "media-typer@0.3.0": "https://registry.npmjs.org/media-typer/-/media-typer-0.3.0.tgz", "media-typer@^1.1.0": "https://registry.npmjs.org/media-typer/-/media-typer-1.1.0.tgz", "memory-pager@^1.0.2": "https://registry.npmjs.org/memory-pager/-/memory-pager-1.5.0.tgz", "merge-descriptors@^2.0.0": "https://registry.npmjs.org/merge-descriptors/-/merge-descriptors-2.0.0.tgz", "mime-db@1.52.0": "https://registry.npmjs.org/mime-db/-/mime-db-1.52.0.tgz", "mime-db@>= 1.43.0 < 2": "https://registry.npmjs.org/mime-db/-/mime-db-1.54.0.tgz", "mime-db@^1.54.0": "https://registry.npmjs.org/mime-db/-/mime-db-1.54.0.tgz", "mime-types@^3.0.0": "https://registry.npmjs.org/mime-types/-/mime-types-3.0.1.tgz", "mime-types@^3.0.1": "https://registry.npmjs.org/mime-types/-/mime-types-3.0.1.tgz", "mime-types@~2.1.24": "https://registry.npmjs.org/mime-types/-/mime-types-2.1.35.tgz", "minimatch@^3.1.2": "https://registry.npmjs.org/minimatch/-/minimatch-3.1.2.tgz", "minimist@^1.2.6": "https://registry.npmjs.org/minimist/-/minimist-1.2.8.tgz", "mkdirp@^0.5.6": "https://registry.npmjs.org/mkdirp/-/mkdirp-0.5.6.tgz", "mongodb-connection-string-url@^3.0.0": "https://registry.npmjs.org/mongodb-connection-string-url/-/mongodb-connection-string-url-3.0.2.tgz", "mongodb@~6.17.0": "https://registry.npmjs.org/mongodb/-/mongodb-6.17.0.tgz", "mongoose@^8.16.1": "https://registry.npmjs.org/mongoose/-/mongoose-8.16.4.tgz", "mpath@0.9.0": "https://registry.npmjs.org/mpath/-/mpath-0.9.0.tgz", "mquery@5.0.0": "https://registry.npmjs.org/mquery/-/mquery-5.0.0.tgz", "ms@2.0.0": "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz", "ms@2.1.3": "https://registry.npmjs.org/ms/-/ms-2.1.3.tgz", "ms@^2.1.1": "https://registry.npmjs.org/ms/-/ms-2.1.3.tgz", "ms@^2.1.3": "https://registry.npmjs.org/ms/-/ms-2.1.3.tgz", "multer-s3@^3.0.1": "https://registry.npmjs.org/multer-s3/-/multer-s3-3.0.1.tgz", "multer@^2.0.2": "https://registry.npmjs.org/multer/-/multer-2.0.2.tgz", "negotiator@^1.0.0": "https://registry.npmjs.org/negotiator/-/negotiator-1.0.0.tgz", "negotiator@~0.6.4": "https://registry.npmjs.org/negotiator/-/negotiator-0.6.4.tgz", "node-addon-api@^8.3.0": "https://registry.npmjs.org/node-addon-api/-/node-addon-api-8.5.0.tgz", "node-domexception@^1.0.0": "https://registry.npmjs.org/node-domexception/-/node-domexception-1.0.0.tgz", "node-fetch@^3.3.2": "https://registry.npmjs.org/node-fetch/-/node-fetch-3.3.2.tgz", "node-gyp-build@^4.8.4": "https://registry.npmjs.org/node-gyp-build/-/node-gyp-build-4.8.4.tgz", "nodemailer@^7.0.4": "https://registry.npmjs.org/nodemailer/-/nodemailer-7.0.5.tgz", "nodemon@^3.1.10": "https://registry.npmjs.org/nodemon/-/nodemon-3.1.10.tgz", "normalize-path@^3.0.0": "https://registry.npmjs.org/normalize-path/-/normalize-path-3.0.0.tgz", "normalize-path@~3.0.0": "https://registry.npmjs.org/normalize-path/-/normalize-path-3.0.0.tgz", "object-assign@^4": "https://registry.npmjs.org/object-assign/-/object-assign-4.1.1.tgz", "object-assign@^4.1.1": "https://registry.npmjs.org/object-assign/-/object-assign-4.1.1.tgz", "object-inspect@^1.13.3": "https://registry.npmjs.org/object-inspect/-/object-inspect-1.13.4.tgz", "on-finished@^2.4.1": "https://registry.npmjs.org/on-finished/-/on-finished-2.4.1.tgz", "on-headers@~1.1.0": "https://registry.npmjs.org/on-headers/-/on-headers-1.1.0.tgz", "once@^1.4.0": "https://registry.npmjs.org/once/-/once-1.4.0.tgz", "parseurl@^1.3.3": "https://registry.npmjs.org/parseurl/-/parseurl-1.3.3.tgz", "path-to-regexp@^8.0.0": "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-8.2.0.tgz", "picomatch@^2.0.4": "https://registry.npmjs.org/picomatch/-/picomatch-2.3.1.tgz", "picomatch@^2.2.1": "https://registry.npmjs.org/picomatch/-/picomatch-2.3.1.tgz", "prisma@^6.11.1": "https://registry.npmjs.org/prisma/-/prisma-6.12.0.tgz", "proxy-addr@^2.0.7": "https://registry.npmjs.org/proxy-addr/-/proxy-addr-2.0.7.tgz", "pstree.remy@^1.1.8": "https://registry.npmjs.org/pstree.remy/-/pstree.remy-1.1.8.tgz", "punycode@^2.3.1": "https://registry.npmjs.org/punycode/-/punycode-2.3.1.tgz", "qs@^6.14.0": "https://registry.npmjs.org/qs/-/qs-6.14.0.tgz", "queue-microtask@^1.2.2": "https://registry.npmjs.org/queue-microtask/-/queue-microtask-1.2.3.tgz", "range-parser@^1.2.1": "https://registry.npmjs.org/range-parser/-/range-parser-1.2.1.tgz", "raw-body@^3.0.0": "https://registry.npmjs.org/raw-body/-/raw-body-3.0.0.tgz", "readable-stream@^3.0.2": "https://registry.npmjs.org/readable-stream/-/readable-stream-3.6.2.tgz", "readable-stream@^3.5.0": "https://registry.npmjs.org/readable-stream/-/readable-stream-3.6.2.tgz", "readdirp@~3.6.0": "https://registry.npmjs.org/readdirp/-/readdirp-3.6.0.tgz", "router@^2.2.0": "https://registry.npmjs.org/router/-/router-2.2.0.tgz", "run-parallel@^1.1.6": "https://registry.npmjs.org/run-parallel/-/run-parallel-1.2.0.tgz", "safe-buffer@5.2.1": "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.2.1.tgz", "safe-buffer@^5.0.1": "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.2.1.tgz", "safe-buffer@~5.2.0": "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.2.1.tgz", "safer-buffer@>= 2.1.2 < 3.0.0": "https://registry.npmjs.org/safer-buffer/-/safer-buffer-2.1.2.tgz", "semver@^7.5.3": "https://registry.npmjs.org/semver/-/semver-7.7.2.tgz", "semver@^7.5.4": "https://registry.npmjs.org/semver/-/semver-7.7.2.tgz", "send@^1.1.0": "https://registry.npmjs.org/send/-/send-1.2.0.tgz", "send@^1.2.0": "https://registry.npmjs.org/send/-/send-1.2.0.tgz", "serve-static@^2.2.0": "https://registry.npmjs.org/serve-static/-/serve-static-2.2.0.tgz", "setprototypeof@1.2.0": "https://registry.npmjs.org/setprototypeof/-/setprototypeof-1.2.0.tgz", "side-channel-list@^1.0.0": "https://registry.npmjs.org/side-channel-list/-/side-channel-list-1.0.0.tgz", "side-channel-map@^1.0.1": "https://registry.npmjs.org/side-channel-map/-/side-channel-map-1.0.1.tgz", "side-channel-weakmap@^1.0.2": "https://registry.npmjs.org/side-channel-weakmap/-/side-channel-weakmap-1.0.2.tgz", "side-channel@^1.1.0": "https://registry.npmjs.org/side-channel/-/side-channel-1.1.0.tgz", "sift@17.1.3": "https://registry.npmjs.org/sift/-/sift-17.1.3.tgz", "simple-update-notifier@^2.0.0": "https://registry.npmjs.org/simple-update-notifier/-/simple-update-notifier-2.0.0.tgz", "sparse-bitfield@^3.0.3": "https://registry.npmjs.org/sparse-bitfield/-/sparse-bitfield-3.0.3.tgz", "statuses@2.0.1": "https://registry.npmjs.org/statuses/-/statuses-2.0.1.tgz", "statuses@^2.0.1": "https://registry.npmjs.org/statuses/-/statuses-2.0.2.tgz", "stream-browserify@3.0.0": "https://registry.npmjs.org/stream-browserify/-/stream-browserify-3.0.0.tgz", "streamsearch@^1.1.0": "https://registry.npmjs.org/streamsearch/-/streamsearch-1.1.0.tgz", "string_decoder@^1.1.1": "https://registry.npmjs.org/string_decoder/-/string_decoder-1.3.0.tgz", "strnum@^2.1.0": "https://registry.npmjs.org/strnum/-/strnum-2.1.1.tgz", "supports-color@^5.5.0": "https://registry.npmjs.org/supports-color/-/supports-color-5.5.0.tgz", "to-regex-range@^5.0.1": "https://registry.npmjs.org/to-regex-range/-/to-regex-range-5.0.1.tgz", "toidentifier@1.0.1": "https://registry.npmjs.org/toidentifier/-/toidentifier-1.0.1.tgz", "touch@^3.1.0": "https://registry.npmjs.org/touch/-/touch-3.1.1.tgz", "tr46@^5.1.0": "https://registry.npmjs.org/tr46/-/tr46-5.1.1.tgz", "ts-node@^10.9.2": "https://registry.npmjs.org/ts-node/-/ts-node-10.9.2.tgz", "tslib@^2.6.2": "https://registry.npmjs.org/tslib/-/tslib-2.8.1.tgz", "type-is@^1.6.18": "https://registry.npmjs.org/type-is/-/type-is-1.6.18.tgz", "type-is@^2.0.0": "https://registry.npmjs.org/type-is/-/type-is-2.0.1.tgz", "type-is@^2.0.1": "https://registry.npmjs.org/type-is/-/type-is-2.0.1.tgz", "typedarray@^0.0.6": "https://registry.npmjs.org/typedarray/-/typedarray-0.0.6.tgz", "typescript@^5.8.3": "https://registry.npmjs.org/typescript/-/typescript-5.8.3.tgz", "undefsafe@^2.0.5": "https://registry.npmjs.org/undefsafe/-/undefsafe-2.0.5.tgz", "undici-types@~7.8.0": "https://registry.npmjs.org/undici-types/-/undici-types-7.8.0.tgz", "unpipe@1.0.0": "https://registry.npmjs.org/unpipe/-/unpipe-1.0.0.tgz", "util-deprecate@^1.0.1": "https://registry.npmjs.org/util-deprecate/-/util-deprecate-1.0.2.tgz", "uuid@^9.0.1": "https://registry.npmjs.org/uuid/-/uuid-9.0.1.tgz", "v8-compile-cache-lib@^3.0.1": "https://registry.npmjs.org/v8-compile-cache-lib/-/v8-compile-cache-lib-3.0.1.tgz", "vary@^1": "https://registry.npmjs.org/vary/-/vary-1.1.2.tgz", "vary@^1.1.2": "https://registry.npmjs.org/vary/-/vary-1.1.2.tgz", "vary@~1.1.2": "https://registry.npmjs.org/vary/-/vary-1.1.2.tgz", "web-streams-polyfill@^3.0.3": "https://registry.npmjs.org/web-streams-polyfill/-/web-streams-polyfill-3.3.3.tgz", "webidl-conversions@^7.0.0": "https://registry.npmjs.org/webidl-conversions/-/webidl-conversions-7.0.0.tgz", "whatwg-url@^14.1.0 || ^13.0.0": "https://registry.npmjs.org/whatwg-url/-/whatwg-url-14.2.0.tgz", "wrappy@1": "https://registry.npmjs.org/wrappy/-/wrappy-1.0.2.tgz", "xtend@^4.0.2": "https://registry.npmjs.org/xtend/-/xtend-4.0.2.tgz", "yn@3.1.1": "https://registry.npmjs.org/yn/-/yn-3.1.1.tgz"}, "files": [], "artifacts": {"@prisma/engines@6.12.0": ["query_engine-windows.dll.node", "schema-engine-windows.exe"]}}