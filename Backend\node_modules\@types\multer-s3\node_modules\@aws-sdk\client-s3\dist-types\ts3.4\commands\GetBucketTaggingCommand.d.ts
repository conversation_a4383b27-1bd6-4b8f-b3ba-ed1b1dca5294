import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import {
  GetBucketTaggingOutput,
  GetBucketTaggingRequest,
} from "../models/models_0";
import {
  S3ClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../S3Client";
export { __MetadataBearer };
export { $Command };
export interface GetBucketTaggingCommandInput extends GetBucketTaggingRequest {}
export interface GetBucketTaggingCommandOutput
  extends GetBucketTaggingOutput,
    __MetadataBearer {}
declare const GetBucketTaggingCommand_base: {
  new (
    input: GetBucketTaggingCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    GetBucketTaggingCommandInput,
    GetBucketTaggingCommandOutput,
    S3ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: GetBucketTaggingCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    GetBucketTaggingCommandInput,
    GetBucketTaggingCommandOutput,
    S3ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class GetBucketTaggingCommand extends GetBucketTaggingCommand_base {
  protected static __types: {
    api: {
      input: GetBucketTaggingRequest;
      output: GetBucketTaggingOutput;
    };
    sdk: {
      input: GetBucketTaggingCommandInput;
      output: GetBucketTaggingCommandOutput;
    };
  };
}
