import { SecureUrlService } from '../services/secureUrlService';
import { checkCloudFrontConfiguration } from '../config/cloudFrontConfig';

/**
 * Utility functions for testing and debugging secure URL functionality
 */
export class SecureUrlTestHelper {
  /**
   * Test CloudFront configuration
   */
  static testConfiguration(): void {
    console.log('🔍 Testing CloudFront Configuration...');
    
    const requiredEnvVars = [
      'CLOUDFRONT_DISTRIBUTION_DOMAIN',
      'CLOUDFRONT_KEY_PAIR_ID',
      'CLOUDFRONT_PRIVATE_KEY',
    ];

    const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);
    
    if (missingVars.length > 0) {
      console.error('❌ Missing environment variables:', missingVars);
      return;
    }

    console.log('✅ All required environment variables are set');
    
    const isConfigured = checkCloudFrontConfiguration();
    if (isConfigured) {
      console.log('✅ CloudFront configuration is valid');
    } else {
      console.error('❌ CloudFront configuration is invalid');
    }

    // Test private key format
    const privateKey = process.env.CLOUDFRONT_PRIVATE_KEY!;
    if (privateKey.includes('\\n')) {
      console.log('✅ Private key appears to have escaped newlines (correct for env var)');
    } else if (privateKey.includes('\n')) {
      console.log('✅ Private key appears to have actual newlines');
    } else {
      console.warn('⚠️  Private key format may be incorrect (no newlines detected)');
    }
  }

  /**
   * Test generating a signed URL for a sample image
   */
  static async testImageUrl(sampleKey: string = 'course-images/test-image.jpg'): Promise<void> {
    console.log('🔍 Testing image URL generation...');
    
    try {
      const result = await SecureUrlService.generateSecureImageUrl(sampleKey, { expirationHours: 1 });
      console.log('✅ Image URL generated successfully');
      console.log('📋 Sample response:', {
        originalKey: sampleKey,
        signedUrl: result.signedUrl.substring(0, 100) + '...',
        expiresAt: result.expiresAt,
        expirationHours: result.expirationHours,
      });
    } catch (error) {
      console.error('❌ Failed to generate image URL:', error);
    }
  }

  /**
   * Test generating a signed URL for a sample video
   */
  static async testVideoUrl(sampleKey: string = 'course-videos/test-video.mp4'): Promise<void> {
    console.log('🔍 Testing video URL generation...');
    
    try {
      const result = await SecureUrlService.generateSecureVideoUrl(sampleKey, { expirationHours: 2 });
      console.log('✅ Video URL generated successfully');
      console.log('📋 Sample response:', {
        originalKey: sampleKey,
        signedUrl: result.signedUrl.substring(0, 100) + '...',
        expiresAt: result.expiresAt,
        expirationHours: result.expirationHours,
      });
    } catch (error) {
      console.error('❌ Failed to generate video URL:', error);
    }
  }

  /**
   * Test URL extraction utility
   */
  static testUrlExtraction(): void {
    console.log('🔍 Testing URL extraction...');
    
    const testCases = [
      'course-images/test.jpg',
      'https://bucket.s3.region.amazonaws.com/course-images/test.jpg',
      'https://d123456789.cloudfront.net/course-videos/test.mp4',
    ];

    testCases.forEach(testUrl => {
      const extracted = SecureUrlService.extractKey(testUrl);
      console.log(`📋 "${testUrl}" → "${extracted}"`);
    });
  }

  /**
   * Run all tests
   */
  static async runAllTests(): Promise<void> {
    console.log('🚀 Running CloudFront Secure URL Tests...\n');
    
    this.testConfiguration();
    console.log('');
    
    this.testUrlExtraction();
    console.log('');
    
    await this.testImageUrl();
    console.log('');
    
    await this.testVideoUrl();
    console.log('');
    
    console.log('✅ All tests completed!');
  }
}

// Export for use in scripts or debugging
export default SecureUrlTestHelper;
