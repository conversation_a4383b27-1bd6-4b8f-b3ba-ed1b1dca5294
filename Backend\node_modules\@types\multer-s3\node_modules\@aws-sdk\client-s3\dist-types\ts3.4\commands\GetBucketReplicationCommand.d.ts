import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import {
  GetBucketReplicationOutput,
  GetBucketReplicationRequest,
} from "../models/models_0";
import {
  S3ClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../S3Client";
export { __MetadataBearer };
export { $Command };
export interface GetBucketReplicationCommandInput
  extends GetBucketReplicationRequest {}
export interface GetBucketReplicationCommandOutput
  extends GetBucketReplicationOutput,
    __MetadataBearer {}
declare const GetBucketReplicationCommand_base: {
  new (
    input: GetBucketReplicationCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    GetBucketReplicationCommandInput,
    GetBucketReplicationCommandOutput,
    S3ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: GetBucketReplicationCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    GetBucketReplicationCommandInput,
    GetBucketReplicationCommandOutput,
    S3ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class GetBucketReplicationCommand extends GetBucketReplicationCommand_base {
  protected static __types: {
    api: {
      input: GetBucketReplicationRequest;
      output: GetBucketReplicationOutput;
    };
    sdk: {
      input: GetBucketReplicationCommandInput;
      output: GetBucketReplicationCommandOutput;
    };
  };
}
