import { ChecksumConstructor, HashConstructor } from "@smithy/types";
import { PreviouslyResolved } from "./configuration";
import { ChecksumAlgorithm } from "./constants";
/**
 * Returns the function that will compute the checksum for the given {@link ChecksumAlgorithm}.
 */
export declare const selectChecksumAlgorithmFunction: (checksumAlgorithm: ChecksumAlgorithm, config: PreviouslyResolved) => ChecksumConstructor | HashConstructor;
