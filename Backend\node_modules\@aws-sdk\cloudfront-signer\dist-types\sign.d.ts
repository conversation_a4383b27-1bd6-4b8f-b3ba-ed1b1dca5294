/**
 * Input type to getSignedUrl and getSignedCookies.
 * @public
 */
export type CloudfrontSignInput = CloudfrontSignInputWithParameters | CloudfrontSignInputWithPolicy;
/**
 * @public
 */
export type CloudfrontSignerCredentials = {
    /** The ID of the Cloudfront key pair. */
    keyPairId: string;
    /** The content of the Cloudfront private key. */
    privateKey: string | Buffer;
    /** The passphrase of RSA-SHA1 key*/
    passphrase?: string;
};
/**
 * @public
 */
export type CloudfrontSignInputWithParameters = CloudfrontSignerCredentials & {
    /** The URL string to sign. */
    url: string;
    /** The date string for when the signed URL or cookie can no longer be accessed */
    dateLessThan: string | number | Date;
    /** The date string for when the signed URL or cookie can start to be accessed. */
    dateGreaterThan?: string | number | Date;
    /** The IP address string to restrict signed URL access to. */
    ipAddress?: string;
    /**
     * [policy] should not be provided when using separate
     * dateLessThan, dateGreaterThan, or ipAddress inputs.
     */
    policy?: never;
};
/**
 * @public
 */
export type CloudfrontSignInputWithPolicy = CloudfrontSignerCredentials & {
    /**
     * The URL string to sign. Optional when policy is provided.
     *
     * This will be used as the initial url if calling getSignedUrl
     * with a policy.
     *
     * This will be ignored if calling getSignedCookies with a policy.
     */
    url?: string;
    /** The JSON-encoded policy string */
    policy: string;
    /** When using a policy, a separate dateLessThan should not be provided. */
    dateLessThan?: never;
    /** When using a policy, a separate dateGreaterThan should not be provided. */
    dateGreaterThan?: never;
    /** When using a policy, a separate ipAddress should not be provided.  */
    ipAddress?: never;
};
/**
 * @public
 */
export interface CloudfrontSignedCookiesOutput {
    /** ID of the Cloudfront key pair. */
    "CloudFront-Key-Pair-Id": string;
    /** Hashed, signed, and base64-encoded version of the JSON policy. */
    "CloudFront-Signature": string;
    /** The unix date time for when the signed URL or cookie can no longer be accessed. */
    "CloudFront-Expires"?: number;
    /** Base64-encoded version of the JSON policy. */
    "CloudFront-Policy"?: string;
}
/**
 * Creates a signed URL string using a canned or custom policy.
 * @public
 * @returns the input URL with signature attached as query parameters.
 */
export declare function getSignedUrl({ dateLessThan, dateGreaterThan, url, keyPairId, privateKey, ipAddress, policy, passphrase, }: CloudfrontSignInput): string;
/**
 * Creates signed cookies using a canned or custom policy.
 * @public
 * @returns an object with keys/values that can be added to cookies.
 */
export declare function getSignedCookies({ ipAddress, url, privateKey, keyPairId, dateLessThan, dateGreaterThan, policy, passphrase, }: CloudfrontSignInput): CloudfrontSignedCookiesOutput;
/**
 * @deprecated use CloudfrontSignInput, CloudfrontSignInputWithParameters, or CloudfrontSignInputWithPolicy.
 */
export type CloudfrontSignInputBase = {
    url: string;
    keyPairId: string;
    privateKey: string | Buffer;
    passphrase?: string;
    dateLessThan?: string;
    ipAddress?: string;
    dateGreaterThan?: string;
};
