import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import { DeleteBucketReplicationRequest } from "../models/models_0";
import {
  S3ClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../S3Client";
export { __MetadataBearer };
export { $Command };
export interface DeleteBucketReplicationCommandInput
  extends DeleteBucketReplicationRequest {}
export interface DeleteBucketReplicationCommandOutput
  extends __MetadataBearer {}
declare const DeleteBucketReplicationCommand_base: {
  new (
    input: DeleteBucketReplicationCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteBucketReplicationCommandInput,
    DeleteBucketReplicationCommandOutput,
    S3ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DeleteBucketReplicationCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteBucketReplicationCommandInput,
    DeleteBucketReplicationCommandOutput,
    S3ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DeleteBucketReplicationCommand extends DeleteBucketReplicationCommand_base {
  protected static __types: {
    api: {
      input: DeleteBucketReplicationRequest;
      output: {};
    };
    sdk: {
      input: DeleteBucketReplicationCommandInput;
      output: DeleteBucketReplicationCommandOutput;
    };
  };
}
