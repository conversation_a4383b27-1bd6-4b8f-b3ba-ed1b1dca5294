import { Command as $Command } from "@smithy/smithy-client";
import { <PERSON>ada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import { DeleteBucketAnalyticsConfigurationRequest } from "../models/models_0";
import {
  S3ClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../S3Client";
export { __MetadataBearer };
export { $Command };
export interface DeleteBucketAnalyticsConfigurationCommandInput
  extends DeleteBucketAnalyticsConfigurationRequest {}
export interface DeleteBucketAnalyticsConfigurationCommandOutput
  extends __MetadataBearer {}
declare const DeleteBucketAnalyticsConfigurationCommand_base: {
  new (
    input: DeleteBucketAnalyticsConfigurationCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteBucketAnalyticsConfigurationCommandInput,
    DeleteBucketAnalyticsConfigurationCommandOutput,
    S3ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DeleteBucketAnalyticsConfigurationCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteBucketAnalyticsConfigurationCommandInput,
    DeleteBucketAnalyticsConfigurationCommandOutput,
    S3ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DeleteBucketAnalyticsConfigurationCommand extends DeleteBucketAnalyticsConfigurationCommand_base {
  protected static __types: {
    api: {
      input: DeleteBucketAnalyticsConfigurationRequest;
      output: {};
    };
    sdk: {
      input: DeleteBucketAnalyticsConfigurationCommandInput;
      output: DeleteBucketAnalyticsConfigurationCommandOutput;
    };
  };
}
