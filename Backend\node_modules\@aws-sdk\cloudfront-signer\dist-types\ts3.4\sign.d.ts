export type CloudfrontSignInput =
  | CloudfrontSignInputWithParameters
  | CloudfrontSignInputWithPolicy;
export type CloudfrontSignerCredentials = {
  keyPairId: string;
  privateKey: string | Buffer;
  passphrase?: string;
};
export type CloudfrontSignInputWithParameters = CloudfrontSignerCredentials & {
  url: string;
  dateLessThan: string | number | Date;
  dateGreaterThan?: string | number | Date;
  ipAddress?: string;
  policy?: never;
};
export type CloudfrontSignInputWithPolicy = CloudfrontSignerCredentials & {
  url?: string;
  policy: string;
  dateLessThan?: never;
  dateGreaterThan?: never;
  ipAddress?: never;
};
export interface CloudfrontSignedCookiesOutput {
  "CloudFront-Key-Pair-Id": string;
  "CloudFront-Signature": string;
  "CloudFront-Expires"?: number;
  "CloudFront-Policy"?: string;
}
export declare function getSignedUrl({
  dateLessThan,
  dateGreaterThan,
  url,
  keyPairId,
  privateKey,
  ipAddress,
  policy,
  passphrase,
}: CloudfrontSignInput): string;
export declare function getSignedCookies({
  ipAddress,
  url,
  privateKey,
  keyPairId,
  dateLessThan,
  dateGreaterThan,
  policy,
  passphrase,
}: CloudfrontSignInput): CloudfrontSignedCookiesOutput;
export type CloudfrontSignInputBase = {
  url: string;
  keyPairId: string;
  privateKey: string | Buffer;
  passphrase?: string;
  dateLessThan?: string;
  ipAddress?: string;
  dateGreaterThan?: string;
};
