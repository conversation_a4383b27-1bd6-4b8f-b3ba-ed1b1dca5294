import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  GetBucketRequestPaymentOutput,
  GetBucketRequestPaymentRequest,
} from "../models/models_0";
import {
  S3ClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../S3Client";
export { __MetadataBearer };
export { $Command };
export interface GetBucketRequestPaymentCommandInput
  extends GetBucketRequestPaymentRequest {}
export interface GetBucketRequestPaymentCommandOutput
  extends GetBucketRequestPaymentOutput,
    __MetadataBearer {}
declare const GetBucketRequestPaymentCommand_base: {
  new (
    input: GetBucketRequestPaymentCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    GetBucketRequestPaymentCommandInput,
    GetBucketRequestPaymentCommandOutput,
    S3ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: GetBucketRequestPaymentCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    GetBucketRequestPaymentCommandInput,
    GetBucketRequestPaymentCommandOutput,
    S3ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class GetBucketRequestPaymentCommand extends GetBucketRequestPaymentCommand_base {
  protected static __types: {
    api: {
      input: GetBucketRequestPaymentRequest;
      output: GetBucketRequestPaymentOutput;
    };
    sdk: {
      input: GetBucketRequestPaymentCommandInput;
      output: GetBucketRequestPaymentCommandOutput;
    };
  };
}
