import { Provider } from "@smithy/types";
import { AccountIdEndpointMode } from "./AccountIdEndpointModeConstants";
/**
 * @public
 */
export interface AccountIdEndpointModeInputConfig {
    /**
     * The account ID endpoint mode to use.
     */
    accountIdEndpointMode?: AccountIdEndpointMode | Provider<AccountIdEndpointMode>;
}
/**
 * @internal
 */
interface PreviouslyResolved {
}
/**
 * @internal
 */
export interface AccountIdEndpointModeResolvedConfig {
    accountIdEndpointMode: Provider<AccountIdEndpointMode>;
}
/**
 * @internal
 */
export declare const resolveAccountIdEndpointModeConfig: <T>(input: T & AccountIdEndpointModeInputConfig & PreviouslyResolved) => T & AccountIdEndpointModeResolvedConfig;
export {};
