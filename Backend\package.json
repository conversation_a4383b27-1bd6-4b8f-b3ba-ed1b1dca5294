{"name": "ptebydee_backend", "version": "1.0.0", "main": "index.js", "scripts": {"start": "node dist/server.js", "dev": "nodemon", "build": "tsc", "prisma:generate": "npx prisma generate", "prisma:push": "npx prisma db push"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@prisma/client": "^6.11.1", "@types/multer": "^2.0.0", "aws-sdk": "^2.1692.0", "bcrypt": "^6.0.0", "compression": "^1.8.0", "cors": "^2.8.5", "dotenv": "^17.0.0", "express": "^5.1.0", "express-async-handler": "^1.2.0", "google-auth-library": "^10.1.0", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.16.1", "multer": "^2.0.2", "multer-s3": "^2.10.0", "nodemailer": "^7.0.4"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/compression": "^1.8.1", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/jsonwebtoken": "^9.0.10", "@types/ms": "^2.1.0", "@types/multer-s3": "^2.7.11", "@types/node": "^24.0.7", "@types/nodemailer": "^6.4.17", "nodemon": "^3.1.10", "prisma": "^6.11.1", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}