import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import {
  GetObjectLockConfigurationOutput,
  GetObjectLockConfigurationRequest,
} from "../models/models_0";
import {
  S3ClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../S3Client";
export { __MetadataBearer };
export { $Command };
export interface GetObjectLockConfigurationCommandInput
  extends GetObjectLockConfigurationRequest {}
export interface GetObjectLockConfigurationCommandOutput
  extends GetObjectLockConfigurationOutput,
    __MetadataBearer {}
declare const GetObjectLockConfigurationCommand_base: {
  new (
    input: GetObjectLockConfigurationCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    GetObjectLockConfigurationCommandInput,
    GetObjectLockConfigurationCommandOutput,
    S3ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: GetObjectLockConfigurationCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    GetObjectLockConfigurationCommandInput,
    GetObjectLockConfigurationCommandOutput,
    S3ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class GetObjectLockConfigurationCommand extends GetObjectLockConfigurationCommand_base {
  protected static __types: {
    api: {
      input: GetObjectLockConfigurationRequest;
      output: GetObjectLockConfigurationOutput;
    };
    sdk: {
      input: GetObjectLockConfigurationCommandInput;
      output: GetObjectLockConfigurationCommandOutput;
    };
  };
}
