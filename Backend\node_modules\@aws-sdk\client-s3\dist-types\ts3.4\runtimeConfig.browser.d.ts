import { FetchHttpHandler as RequestHandler } from "@smithy/fetch-http-handler";
import { S3ClientConfig } from "./S3Client";
export declare const getRuntimeConfig: (config: S3ClientConfig) => {
  runtime: string;
  defaultsMode: import("@smithy/types").Provider<
    import("@smithy/smithy-client").ResolvedDefaultsMode
  >;
  bodyLengthChecker: import("@smithy/types").BodyLengthCalculator;
  credentialDefaultProvider:
    | ((input: any) => import("@smithy/types").AwsCredentialIdentityProvider)
    | ((
        _: unknown
      ) => () => Promise<import("@smithy/types").AwsCredentialIdentity>);
  defaultUserAgentProvider: (
    config?: import("@aws-sdk/util-user-agent-browser").PreviouslyResolved
  ) => Promise<import("@smithy/types").UserAgent>;
  eventStreamSerdeProvider: import("@smithy/types").EventStreamSerdeProvider;
  maxAttempts: number | import("@smithy/types").Provider<number>;
  md5: import("@smithy/types").HashConstructor;
  region: string | import("@smithy/types").Provider<any>;
  requestHandler:
    | import("@smithy/protocol-http").HttpHandler<any>
    | RequestHandler;
  retryMode: string | import("@smithy/types").Provider<string>;
  sha1: import("@smithy/types").HashConstructor;
  sha256: import("@smithy/types").HashConstructor;
  streamCollector: import("@smithy/types").StreamCollector;
  streamHasher:
    | import("@smithy/types").StreamHasher<import("stream").Readable>
    | import("@smithy/types").StreamHasher<Blob>;
  useDualstackEndpoint: (boolean | import("@smithy/types").Provider<boolean>) &
    (boolean | import("@smithy/types").Provider<boolean | undefined>);
  useFipsEndpoint: (boolean | import("@smithy/types").Provider<boolean>) &
    (boolean | import("@smithy/types").Provider<boolean | undefined>);
  apiVersion: string;
  cacheMiddleware?: boolean | undefined;
  urlParser: import("@smithy/types").UrlParser;
  base64Decoder: import("@smithy/types").Decoder;
  base64Encoder: (_input: Uint8Array | string) => string;
  utf8Decoder: import("@smithy/types").Decoder;
  utf8Encoder: (input: Uint8Array | string) => string;
  disableHostPrefix: boolean;
  serviceId: string;
  profile?: string;
  getAwsChunkedEncodingStream:
    | import("@smithy/types").GetAwsChunkedEncodingStream<any>
    | import("@smithy/types").GetAwsChunkedEncodingStream<
        import("stream").Readable
      >;
  logger: import("@smithy/types").Logger;
  extensions: import("./runtimeExtensions").RuntimeExtension[];
  signingEscapePath: boolean;
  useArnRegion:
    | boolean
    | undefined
    | import("@smithy/types").Provider<boolean | undefined>;
  sdkStreamMixin: import("@smithy/types").SdkStreamMixinInjector;
  customUserAgent?: string | import("@smithy/types").UserAgent;
  userAgentAppId?:
    | string
    | undefined
    | import("@smithy/types").Provider<string | undefined>;
  requestChecksumCalculation?:
    | import("@aws-sdk/middleware-flexible-checksums").RequestChecksumCalculation
    | import("@smithy/types").Provider<
        import("@aws-sdk/middleware-flexible-checksums").RequestChecksumCalculation
      >;
  responseChecksumValidation?:
    | import("@aws-sdk/middleware-flexible-checksums").ResponseChecksumValidation
    | import("@smithy/types").Provider<
        import("@aws-sdk/middleware-flexible-checksums").ResponseChecksumValidation
      >;
  requestStreamBufferSize?: number | false;
  retryStrategy?:
    | import("@smithy/types").RetryStrategy
    | import("@smithy/types").RetryStrategyV2;
  endpoint?:
    | ((
        | string
        | import("@smithy/types").Endpoint
        | import("@smithy/types").Provider<import("@smithy/types").Endpoint>
        | import("@smithy/types").EndpointV2
        | import("@smithy/types").Provider<import("@smithy/types").EndpointV2>
      ) &
        (
          | string
          | import("@smithy/types").Provider<string>
          | import("@smithy/types").Endpoint
          | import("@smithy/types").Provider<import("@smithy/types").Endpoint>
          | import("@smithy/types").EndpointV2
          | import("@smithy/types").Provider<import("@smithy/types").EndpointV2>
        ))
    | undefined;
  endpointProvider: (
    endpointParams: import("./endpoint/EndpointParameters").EndpointParameters,
    context?: {
      logger?: import("@smithy/types").Logger;
    }
  ) => import("@smithy/types").EndpointV2;
  tls?: boolean;
  serviceConfiguredEndpoint?: never;
  authSchemePreference?: string[] | import("@smithy/types").Provider<string[]>;
  httpAuthSchemes: import("@smithy/types").HttpAuthScheme[];
  httpAuthSchemeProvider: import("./auth/httpAuthSchemeProvider").S3HttpAuthSchemeProvider;
  credentials?:
    | import("@smithy/types").AwsCredentialIdentity
    | import("@smithy/types").AwsCredentialIdentityProvider;
  signer?:
    | import("@smithy/types").RequestSigner
    | ((
        authScheme?: import("@smithy/types").AuthScheme
      ) => Promise<import("@smithy/types").RequestSigner>);
  systemClockOffset?: number;
  signingRegion?: string;
  signerConstructor:
    | typeof import("@aws-sdk/signature-v4-multi-region").SignatureV4MultiRegion
    | (new (
        options: import("@smithy/signature-v4").SignatureV4Init &
          import("@smithy/signature-v4").SignatureV4CryptoInit
      ) => import("@smithy/types").RequestSigner);
  sigv4aSigningRegionSet?:
    | string[]
    | undefined
    | import("@smithy/types").Provider<string[] | undefined>;
  forcePathStyle?:
    | (boolean &
        (boolean | import("@smithy/types").Provider<boolean | undefined>))
    | undefined;
  useAccelerateEndpoint?:
    | (boolean &
        (boolean | import("@smithy/types").Provider<boolean | undefined>))
    | undefined;
  disableMultiregionAccessPoints?:
    | (boolean &
        (boolean | import("@smithy/types").Provider<boolean | undefined>))
    | undefined;
  followRegionRedirects?: boolean;
  s3ExpressIdentityProvider?: import("@aws-sdk/middleware-sdk-s3").S3ExpressIdentityProvider;
  bucketEndpoint?: boolean;
  useGlobalEndpoint?:
    | boolean
    | undefined
    | import("@smithy/types").Provider<boolean | undefined>;
  disableS3ExpressSessionAuth?:
    | boolean
    | undefined
    | import("@smithy/types").Provider<boolean | undefined>;
};
