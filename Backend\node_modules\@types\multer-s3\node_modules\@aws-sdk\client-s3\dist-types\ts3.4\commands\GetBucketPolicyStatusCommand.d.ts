import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  GetBucketPolicyStatusOutput,
  GetBucketPolicyStatusRequest,
} from "../models/models_0";
import {
  S3ClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../S3Client";
export { __MetadataBearer };
export { $Command };
export interface GetBucketPolicyStatusCommandInput
  extends GetBucketPolicyStatusRequest {}
export interface GetBucketPolicyStatusCommandOutput
  extends GetBucketPolicyStatusOutput,
    __MetadataBearer {}
declare const GetBucketPolicyStatusCommand_base: {
  new (
    input: GetBucketPolicyStatusCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    GetBucketPolicyStatusCommandInput,
    GetBucketPolicyStatusCommandOutput,
    S3ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: GetBucketPolicyStatusCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    GetBucketPolicyStatusCommandInput,
    GetBucketPolicyStatusCommandOutput,
    S3ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class GetBucketPolicyStatusCommand extends GetBucketPolicyStatusCommand_base {
  protected static __types: {
    api: {
      input: GetBucketPolicyStatusRequest;
      output: GetBucketPolicyStatusOutput;
    };
    sdk: {
      input: GetBucketPolicyStatusCommandInput;
      output: GetBucketPolicyStatusCommandOutput;
    };
  };
}
